package com.pig4cloud.pigx.xxmg.controller;

import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.pig4cloud.pigx.common.core.util.R;
import com.pig4cloud.pigx.common.security.annotation.Inner;
import com.pig4cloud.pigx.common.security.service.PigxUser;
import com.pig4cloud.pigx.common.security.util.SecurityUtils;
import com.pig4cloud.pigx.xxmg.dto.NewGameRequestDto;
import com.pig4cloud.pigx.xxmg.dto.ReportGameRequestDto;
import com.pig4cloud.pigx.xxmg.service.XxmgGameService;
import com.pig4cloud.pigx.xxmg.vo.GameNextResponseVo;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 连连看游戏控制器
 *
 * <AUTHOR>
 * @date 2025-07-26
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/matchGame")
@Tag(description = "xxmgGame", name = "连连看游戏管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class XxmgGameController {

    private final XxmgGameService gameService;

    /**
     * 获取新一卡配置和单词对
     *
     * @param request 请求参数
     * @return 关卡配置和单词对
     */
    @Operation(summary = "获取新一关卡", description = "获取新一关卡配置和单词对")
    @PostMapping("/new")
    @Inner(value = false) // 跳过权限验证用于API调试
    // @PreAuthorize("@pms.hasPermission('xxmg_game_play')") // 临时移除用于测试
    public R<GameNextResponseVo> getNewGame(@RequestBody NewGameRequestDto request) {
        try {
            return gameService.getNewGame(request);
        } catch (Exception e) {
            log.error("获取新关卡失败", e);
            // 为调试提供更详细的错误信息
            return R.failed("获取游戏配置失败: " + e.getMessage());
        }
    }

    /**
     * 上报游戏结果
     *
     * @param request 游戏结果
     * @return 处理结果
     */
    @Operation(summary = "上报游戏结果", description = "上报游戏结果并更新学习数据")
    @PostMapping("/report")
    @Inner(value = false) // 跳过权限验证用于API调试
    // @PreAuthorize("@pms.hasPermission('xxmg_game_play')") // 临时移除用于测试
    public R<Void> reportGameResult(@RequestBody ReportGameRequestDto request) {
        return gameService.reportGameResult(request);
    }

    /**
     * 查询用户游戏历史记录
     *
     * @param limit 限制条数
     * @return 游戏记录列表
     */
    @Operation(summary = "查询游戏记录", description = "查询用户游戏历史记录")
    @GetMapping("/query/records")
    @Inner(value = false) // 跳过权限验证用于API调试
    // @PreAuthorize("@pms.hasPermission('xxmg_game_view')") // 临时移除用于测试
    public R<?> getUserGameRecords(@RequestParam(defaultValue = "10") Integer limit) {
        // 临时使用固定用户ID进行测试
        Long testUserId = 1L;
        return gameService.getUserGameRecords(testUserId, limit);
    }

    /**
     * 获取用户游戏统计信息
     *
     * @return 统计信息
     */
    @Operation(summary = "获取游戏统计", description = "获取用户游戏统计信息")
    @GetMapping("/query/stats")
    @Inner(value = false) // 跳过权限验证用于API调试
    // @PreAuthorize("@pms.hasPermission('xxmg_game_view')") // 临时移除用于测试
    public R<?> getUserGameStats() {
        // 临时使用固定用户ID进行测试
        Long testUserId = 1L;
        return gameService.getUserGameStats(testUserId);
    }

    /**
     * 获取推荐游戏等级
     *
     * @param mode 游戏模式
     * @return 推荐等级
     */
    @Operation(summary = "获取推荐等级", description = "根据FSRS数据获取推荐游戏等级")
    @GetMapping("/query/level")
    @Inner(value = false) // 跳过认证用于测试
    // @PreAuthorize("@pms.hasPermission('xxmg_game_view')") // 临时移除用于测试
    public R<Integer> getRecommendedLevel(@RequestParam String mode) {
        log.info("进入getRecommendedLevel方法，mode: {}", mode);

        // 临时测试：返回固定等级，不依赖用户信息
        if ("test".equals(mode)) {
            log.info("返回测试等级3");
            return R.ok(3); // 返回测试等级3
        }

        // 正常逻辑（需要认证）
        PigxUser user = SecurityUtils.getUser();
        Integer level = gameService.calculateRecommendedLevel(user.getId(), mode);
        return R.ok(level);
    }
}
